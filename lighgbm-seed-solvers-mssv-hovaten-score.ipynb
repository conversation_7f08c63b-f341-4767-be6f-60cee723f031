{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a6a10712", "metadata": {"_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "execution": {"iopub.execute_input": "2025-05-27T03:13:05.719456Z", "iopub.status.busy": "2025-05-27T03:13:05.719255Z", "iopub.status.idle": "2025-05-27T03:13:07.087839Z", "shell.execute_reply": "2025-05-27T03:13:07.086997Z"}, "papermill": {"duration": 1.372382, "end_time": "2025-05-27T03:13:07.089123", "exception": false, "start_time": "2025-05-27T03:13:05.716741", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/kaggle/input/ds-108-p-21-assigment-06/delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv\n", "/kaggle/input/ds-108-p-21-assigment-06/not_delay_4_6_CONDITION_PRODUCT_SUPPLIER.csv\n", "/kaggle/input/ds-108-p-21-assigment-06/PILOT_10.csv\n", "/kaggle/input/ds-108-p-21-assigment-06/not_delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv\n", "/kaggle/input/ds-108-p-21-assigment-06/sample_Solution.csv\n", "/kaggle/input/ds-108-p-21-assigment-06/delay_7_9_CONDITION_PRODUCT_SUPPLIER.csv\n"]}], "source": ["# This Python 3 environment comes with many helpful analytics libraries installed\n", "# It is defined by the kaggle/python Docker image: https://github.com/kaggle/docker-python\n", "# For example, here's several helpful packages to load\n", "\n", "import numpy as np # linear algebra\n", "import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)\n", "\n", "# Input data files are available in the read-only \"../input/\" directory\n", "# For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory\n", "\n", "import os\n", "for dirname, _, filenames in os.walk('/kaggle/input'):\n", "    for filename in filenames:\n", "        print(os.path.join(dirname, filename))\n", "\n", "# You can write up to 20GB to the current directory (/kaggle/working/) that gets preserved as output when you create a version using \"Save & Run All\" \n", "# You can also write temporary files to /kaggle/temp/, but they won't be saved outside of the current session"]}, {"cell_type": "code", "execution_count": 2, "id": "854873f0", "metadata": {"execution": {"iopub.execute_input": "2025-05-27T03:13:07.093697Z", "iopub.status.busy": "2025-05-27T03:13:07.093343Z", "iopub.status.idle": "2025-05-27T03:13:07.156437Z", "shell.execute_reply": "2025-05-27T03:13:07.155673Z"}, "papermill": {"duration": 0.066429, "end_time": "2025-05-27T03:13:07.157522", "exception": false, "start_time": "2025-05-27T03:13:07.091093", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 125516 entries, 0 to 125515\n", "Data columns (total 2 columns):\n", " #   Column  Non-Null Count   Dtype\n", "---  ------  --------------   -----\n", " 0   ID      125516 non-null  int64\n", " 1   label   125516 non-null  int64\n", "dtypes: int64(2)\n", "memory usage: 1.9 MB\n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv(\"/kaggle/input/ds-108-p-21-assigment-06/sample_Solution.csv\")\n", "\n", "df.info()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a16132d8", "metadata": {"execution": {"iopub.execute_input": "2025-05-27T03:13:07.162011Z", "iopub.status.busy": "2025-05-27T03:13:07.161806Z", "iopub.status.idle": "2025-05-27T03:13:07.258835Z", "shell.execute_reply": "2025-05-27T03:13:07.258305Z"}, "papermill": {"duration": 0.100812, "end_time": "2025-05-27T03:13:07.259943", "exception": false, "start_time": "2025-05-27T03:13:07.159131", "status": "completed"}, "tags": []}, "outputs": [], "source": ["df.to_csv(\"submission.csv\", index= False)"]}, {"cell_type": "code", "execution_count": null, "id": "c09a7960", "metadata": {"papermill": {"duration": 0.00109, "end_time": "2025-05-27T03:13:07.262565", "exception": false, "start_time": "2025-05-27T03:13:07.261475", "status": "completed"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"databundleVersionId": 12476472, "sourceId": 103436, "sourceType": "competition"}], "dockerImageVersionId": 31041, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 5.96101, "end_time": "2025-05-27T03:13:07.579417", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2025-05-27T03:13:01.618407", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}